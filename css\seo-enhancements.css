/* SEO Enhancements CSS */

/* Breadcrumb Section */
.breadcrumb-section {
    background: #f8f9fa;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}

.seo-breadcrumb .breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
    font-size: 14px;
}

.seo-breadcrumb .breadcrumb-item {
    color: #6c757d;
}

.seo-breadcrumb .breadcrumb-item a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.seo-breadcrumb .breadcrumb-item a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.seo-breadcrumb .breadcrumb-item.active {
    color: #495057;
    font-weight: 500;
}

/* Healthcare Keywords Section */
.post-keywords-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 30px 0;
    border-left: 4px solid #007bff;
}

.post-keywords-section h3 {
    color: #495057;
    font-size: 18px;
    margin-bottom: 15px;
    font-weight: 600;
}

.healthcare-keywords {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.keyword-tag {
    background: #007bff;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.keyword-tag:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

/* Enhanced Social Sharing */
.post-social-share {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 25px;
    border-radius: 12px;
    margin: 30px 0;
    text-align: center;
    color: white;
}

.post-social-share h3 {
    color: white;
    margin-bottom: 10px;
    font-size: 20px;
    font-weight: 600;
}

.share-description {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20px;
    font-size: 14px;
}

.social-share-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.social-share {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.social-share:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    color: white;
    text-decoration: none;
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.2);
}

.social-share i {
    font-size: 16px;
}

/* Specific social media colors on hover */
.social-share.facebook:hover {
    background: #1877f2;
    border-color: #1877f2;
}

.social-share.twitter:hover {
    background: #1da1f2;
    border-color: #1da1f2;
}

.social-share.linkedin:hover {
    background: #0077b5;
    border-color: #0077b5;
}

.social-share.whatsapp:hover {
    background: #25d366;
    border-color: #25d366;
}

.social-share.email:hover {
    background: #ea4335;
    border-color: #ea4335;
}

/* Enhanced Post Meta */
.post-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.post-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
}

.post-meta i {
    font-size: 12px;
}

.post-category {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .social-share-buttons {
        gap: 10px;
    }
    
    .social-share {
        padding: 8px 12px;
        font-size: 12px;
    }
    
    .social-share span {
        display: none;
    }
    
    .healthcare-keywords {
        justify-content: center;
    }
    
    .keyword-tag {
        font-size: 11px;
        padding: 5px 10px;
    }
    
    .post-meta {
        justify-content: center;
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .breadcrumb-section {
        padding: 10px 0;
    }
    
    .seo-breadcrumb .breadcrumb {
        font-size: 12px;
    }
    
    .post-keywords-section {
        padding: 15px;
        margin: 20px 0;
    }
    
    .post-social-share {
        padding: 20px 15px;
    }
}

/* SEO-friendly hidden content for screen readers */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Structured data visibility improvements */
time[datetime] {
    font-style: normal;
}

[itemprop] {
    /* Ensure structured data elements are properly styled */
}
