<?php
/**
 * Dynamic Sitemap Generator for Doctors At Door Step
 * Generates XML sitemap with all pages, blog posts, and services
 */

require_once 'includes/Database.php';
require_once 'includes/Blog.php';
require_once 'includes/Services.php';

// Set content type to XML
header('Content-Type: application/xml; charset=utf-8');

// Get current domain
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$domain = $protocol . '://' . $_SERVER['HTTP_HOST'];

// Initialize handlers
$blogHandler = new Blog();
$servicesHandler = new Services();

// Get all published blog posts
$blog_posts = $blogHandler->getAllPublishedPosts();

// Get all active services
$services = $servicesHandler->getAllActiveServices();

// Start XML output
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

// Static pages with healthcare-focused priorities
$static_pages = [
    ['url' => '/', 'priority' => '1.0', 'changefreq' => 'weekly'],
    ['url' => '/about.php', 'priority' => '0.9', 'changefreq' => 'monthly'],
    ['url' => '/services.php', 'priority' => '0.9', 'changefreq' => 'weekly'],
    ['url' => '/booking.php', 'priority' => '0.9', 'changefreq' => 'monthly'],
    ['url' => '/contact.php', 'priority' => '0.8', 'changefreq' => 'monthly'],
    ['url' => '/pricing.php', 'priority' => '0.8', 'changefreq' => 'monthly'],
    ['url' => '/blog.php', 'priority' => '0.7', 'changefreq' => 'weekly'],
    ['url' => '/faq.php', 'priority' => '0.6', 'changefreq' => 'monthly'],
    ['url' => '/terms.php', 'priority' => '0.3', 'changefreq' => 'yearly'],
    ['url' => '/privacy.php', 'priority' => '0.3', 'changefreq' => 'yearly']
];

// Add static pages
foreach ($static_pages as $page) {
    echo "    <url>\n";
    echo "        <loc>" . htmlspecialchars($domain . $page['url']) . "</loc>\n";
    echo "        <lastmod>" . date('Y-m-d') . "</lastmod>\n";
    echo "        <changefreq>" . $page['changefreq'] . "</changefreq>\n";
    echo "        <priority>" . $page['priority'] . "</priority>\n";
    echo "    </url>\n";
}

// Add service detail pages
foreach ($services as $service) {
    echo "    <url>\n";
    echo "        <loc>" . htmlspecialchars($domain . '/service-details.php?id=' . $service['id']) . "</loc>\n";
    echo "        <lastmod>" . date('Y-m-d', strtotime($service['updated_at'] ?? $service['created_at'])) . "</lastmod>\n";
    echo "        <changefreq>weekly</changefreq>\n";
    echo "        <priority>0.8</priority>\n";
    echo "    </url>\n";
}

// Add blog posts
foreach ($blog_posts as $post) {
    echo "    <url>\n";
    echo "        <loc>" . htmlspecialchars($domain . '/blog-post.php?slug=' . $post['slug']) . "</loc>\n";
    echo "        <lastmod>" . date('Y-m-d', strtotime($post['updated_at'] ?? $post['published_at'])) . "</lastmod>\n";
    echo "        <changefreq>monthly</changefreq>\n";
    echo "        <priority>0.6</priority>\n";
    echo "    </url>\n";
}

echo '</urlset>';
?>
