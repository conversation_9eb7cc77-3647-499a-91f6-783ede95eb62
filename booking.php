<?php
// Enhanced SEO for booking page
$GLOBALS['page_title'] = 'Book Doctor at Home Nepal | Schedule Home Doctor Visit Kathmandu | Doctors At Door Step';
$GLOBALS['page_description'] = 'Book doctor at home in Nepal. Schedule home doctor visit in Kathmandu. Easy online booking for home healthcare services. Professional medical care at your doorstep.';
$GLOBALS['meta_keywords'] = 'book doctor at home nepal, schedule home doctor visit, book home healthcare kathmandu, doctor appointment at home, home doctor booking nepal, medical appointment at home, book doorstep doctor service, home healthcare booking';

include 'includes/header.php';
?>

<!-- Hero Banner -->
<section class="page-hero booking-hero" data-aos="fade-up">
    <div class="container">
        <div class="breadcrumb">
            <a href="index.php">Home</a>
            <span class="separator">/</span>
            <span>Book Appointment</span>
        </div>
        <h1>Schedule Your Doctor Visit</h1>
        <p>Book a consultation or medical service with our qualified doctors at your doorstep</p>
    </div>
    <div class="scroll-hint">
        <span>Start booking</span>
        <i class="fas fa-chevron-down"></i>
    </div>
</section>

<!-- Booking Form Section -->
<section class="booking-section" data-aos="fade-up">
    <div class="container">
        <div class="booking-grid">
            <!-- Success Message -->
            <?php if (isset($_GET['success']) && $_GET['success'] == '1'): ?>
            <div class="success-message" data-aos="fade-up">
                <div class="success-content">
                    <i class="fas fa-check-circle"></i>
                    <h3>Booking Request Submitted Successfully!</h3>
                    <p>Thank you for your booking request. We'll contact you within 24 hours to confirm your appointment details.</p>
                    <a href="booking.php" class="btn btn-primary">Submit Another Booking</a>
                </div>
            </div>
            <?php else: ?>
            <!-- Booking Form -->
            <div class="booking-form-wrapper" data-aos="fade-right">
                <h2>Book Your Appointment</h2>
                <p>Fill out the form below to schedule a visit with our qualified doctors at your doorstep</p>

                <form id="bookingForm" action="https://formspree.io/f/xqabgezq" method="POST">
                    <!-- Hidden fields for Formspree -->
                    <input type="hidden" name="_subject" value="New Booking Request - Doctors at Doorstep">
                    <input type="hidden" name="_next" value="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?>/booking.php?success=1">
                    
                    <div class="form-content">
                        <div class="form-group">
                            <label for="name">Full Name *</label>
                            <input type="text" id="name" name="name" placeholder="Enter your full name" required>
                        </div>

                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" placeholder="Enter your email address" required>
                        </div>

                        <div class="form-group">
                            <label for="phone">Phone Number *</label>
                            <input type="tel" id="phone" name="phone" placeholder="Enter your phone number" required>
                        </div>

                        <div class="form-group">
                            <label for="service">Select Service *</label>
                            <select id="service" name="service" required>
                                <option value="">Choose a service...</option>
                                <option value="Doctor-on-call">Doctor On Call</option>
                                <option value="Nursing-service">Nursing Service</option>
                                <option value="caregiver-service">Caregiver Service</option> 
                                <option value="lab-on-call">Lab on Call</option>
                                <option value="Physiotherapy">Physiotherapy</option> 
                                <option value="I/V-injection">I/V Injection</option>
                                <option value="Medical-procedure">Medical Procedure</option>                    
                                <option value="Medicine-delivery">Medicine Delivery</option>
                                <option value="Medical-equipment-rent-and-sell">Medical Equipment rent and sell</option>
                                <option value="ECG">ECG</option>
                                <option value="mothers-and-baby-care">Mothers and Baby Care</option>
                                <option value="Councellor-at-Home">Councellor at Home</option>

                            </select>
                        </div>

                        <div class="form-group">
                            <label for="preferredDate">Preferred Date *</label>
                            <input type="date" id="preferredDate" name="preferredDate" required>
                        </div>

                        <div class="form-group">
                            <label for="preferredTime">Preferred Time *</label>
                            <select id="preferredTime" name="preferredTime" required>
                                <option value="">Choose a time...</option>
                                <option value="morning">Morning (9:00 AM - 12:00 PM)</option>
                                <option value="afternoon">Afternoon (12:00 PM - 4:00 PM)</option>
                                <option value="evening">Evening (4:00 PM - 8:00 PM)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="careType">Type of Care *</label>
                            <select id="careType" name="careType" required>
                                <option value="">Select care type...</option>
                                <option value="one-time">One-time Visit</option>
                                <option value="recurring">Recurring Care</option>
                                <option value="consultation">Initial Consultation</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="medicalCondition">Medical Condition/Concerns</label>
                            <textarea id="medicalCondition" name="medicalCondition" rows="4" placeholder="Please describe any medical conditions or concerns (optional)"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="emergencyContact">Emergency Contact Name *</label>
                            <input type="text" id="emergencyContact" name="emergencyContact" placeholder="Enter emergency contact name" required>
                        </div>

                        <div class="form-group">
                            <label for="emergencyPhone">Emergency Contact Phone *</label>
                            <input type="tel" id="emergencyPhone" name="emergencyPhone" placeholder="Enter emergency contact phone" required>
                        </div>
                    
                        <!-- Terms and Conditions -->
                        <div class="form-group checkbox-group">
                            <input type="checkbox" id="terms" name="terms" required>
                            <label for="terms">I agree to the <a href="terms.php">Terms and Conditions</a> and <a href="privacy.php">Privacy Policy</a> *</label>
                        </div>
                        <button type="submit" class="btn btn-primary">Schedule Appointment <i class="fas fa-calendar-check"></i></button>
                    </div>
                </form>
            </div>
            <?php endif; ?>

            <!-- Booking Information -->
            <div class="booking-info-wrapper" data-aos="fade-left">
                <div class="booking-info-box">
                    <h2>Booking Information</h2>
                    <div class="info-section">
                        <h3>What to Expect</h3>
                        <ul>
                            <li>Confirmation within 24 hours</li>
                            <li>Professional healthcare staff</li>
                            <li>Personalized care plan</li>
                            <li>Free initial consultation</li>
                        </ul>
                    </div>

                    <div class="info-section">
                        <h3>Need Help?</h3>
                        <p>Contact our medical coordinators for assistance:</p>
                        <a href="tel:+97701456789" class="contact-phone">
                            <i class="fas fa-phone"></i>
                            +977 ***********
                        </a>
                        <p class="availability">Available Monday - Friday, 9:00 AM - 6:00 PM</p>
                    </div>
                </div>
            </div>
    </div>
</section>

<!-- JavaScript for form validation -->
<script src="js/booking_simple.js"></script>

<?php include 'includes/footer.php'; ?>
