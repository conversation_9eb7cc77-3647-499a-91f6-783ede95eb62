<?php
define('ALLOWED_ACCESS', true);

// Enhanced SEO for homepage
$GLOBALS['page_title'] = 'Doctor at Home Service Nepal | Call Doctor at Home Kathmandu | Doctors At Door Step';
$GLOBALS['page_description'] = 'Professional doctor at home service in Nepal. Call doctor at home in Kathmandu for medical consultations, home nursing, elderly care. 24/7 doorstep healthcare services. Book now!';
$GLOBALS['meta_keywords'] = 'doctor at home service nepal, call doctor at home kathmandu, home doctor service nepal, doorstep doctor service, home healthcare nepal, medical care at home kathmandu, home nursing service nepal, doctor visit at home, healthcare at doorstep nepal, home medical consultation, doctors at door step, home healthcare kathmandu, medical services at home, nursing care at home nepal';

include 'includes/header.php';

// Get services for display
require_once 'includes/Services.php';
$servicesHandler = new Services();
$featuredServices = array_slice($servicesHandler->getAllActiveServices(), 0, 3);

// Get partners for display
require_once 'includes/Partners.php';
$partnersHandler = new Partners();
$partners = $partnersHandler->getActivePartners();
?>

<!-- Hero Section with Enhanced Background Slider -->
<section class="hero" data-aos="fade">
    <!-- Hero Background Slider -->
    <div class="hero-slider">
        <div class="swiper-container hero-swiper">
            <div class="swiper-wrapper">
                <!-- Slide 1: Medical Care at Home -->
                <div class="swiper-slide hero-slide" style="background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('images/hero-1.webp')">
                    <div class="slide-overlay"></div>
                    <div class="slide-content">
                        <div class="slide-badge">
                            <i class="fas fa-home"></i>
                            <span>Home Care</span>
                        </div>
                    </div>
                </div>

                <!-- Slide 2: Professional Doctors -->
                <div class="swiper-slide hero-slide" style="background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('images/hero-2.webp')">
                    <div class="slide-overlay"></div>
                    <div class="slide-content">
                        <div class="slide-badge">
                            <i class="fas fa-user-md"></i>
                            <span>Expert Doctors</span>
                        </div>
                    </div>
                </div>

                <!-- Slide 3: Medical Equipment -->
                <div class="swiper-slide hero-slide" style="background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('images/logo.webp')">
                    <div class="slide-overlay"></div>
                    <div class="slide-content">
                        <div class="slide-badge">
                            <i class="fas fa-stethoscope"></i>
                            <span>Modern Equipment</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Animated Background Elements -->
    <div class="hero-bg-elements">
        <div class="floating-element element-1">
            <i class="fas fa-heartbeat"></i>
        </div>
        <div class="floating-element element-2">
            <i class="fas fa-plus"></i>
        </div>
        <div class="floating-element element-3">
            <i class="fas fa-shield-alt"></i>
        </div>
    </div>

    <!-- Hero Content Overlay -->
    <div class="hero-content">
        <div class="hero-content-inner">
            <div class="hero-badge" data-aos="fade-down" data-aos-delay="200">
                <i class="fas fa-award"></i>
                <span>Trusted Healthcare Provider</span>
            </div>
            <h1 data-aos="fade-up" data-aos-delay="400">Doctors At Your Doorstep</h1>
            <p data-aos="fade-up" data-aos-delay="600">
                We offer expert home nursing care in Kathmandu — professional, compassionate, and trusted by families. Discover the best home health care services tailored for comfort, recovery, and peace of mind.
            </p>
            <div class="hero-cta" data-aos="fade-up" data-aos-delay="800">
                <a href="tel:+9779860102404" class="btn btn-primary btn-enhanced">
                    <i class="fas fa-phone"></i>
                    <span>Call Us Now<br>+977 ***********</span>
                </a>
                <a href="services.php" class="btn btn-outline btn-enhanced">
                    <i class="fas fa-list-ul"></i>
                    <span>Our Services</span>
                </a>
            </div>

            <!-- Trust Indicators -->
            <div class="hero-trust-indicators" data-aos="fade-up" data-aos-delay="1000">
                <div class="trust-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Licensed Doctors</span>
                </div>
                <div class="trust-item">
                    <i class="fas fa-clock"></i>
                    <span>24/7 Available</span>
                </div>
                <div class="trust-item">
                    <i class="fas fa-star"></i>
                    <span>5-Star Rated</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Hero Slider Navigation -->
    <div class="hero-slider-dots"></div>

    <!-- Hero Navigation Arrows -->
    <button class="hero-arrow hero-prev">
        <i class="fas fa-chevron-left"></i>
    </button>
    <button class="hero-arrow hero-next">
        <i class="fas fa-chevron-right"></i>
    </button>
</section>

<!-- Services Overview - Dynamic Slider -->
<section class="services-overview">
    <div class="container">
        <div class="section-header" data-aos="fade-up">
            <h2>Our Services</h2>
            <p>Comprehensive healthcare solutions tailored to your needs</p>
        </div>

        <!-- Services Slider -->
        <div class="services-slider-container">
            <?php if (empty($featuredServices)): ?>
                <!-- Fallback static services if no dynamic services available -->
                <div class="services-slider">
                    <div class="swiper-container services-swiper">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide">
                                <div class="service-slide-card">
                                    <div class="service-slide-icon">
                                        <i class="fas fa-user-nurse"></i>
                                    </div>
                                    <h3>Home Nursing</h3>
                                    <div class="service-hover-actions">
                                        <a href="services.php" class="service-action-btn know-more">Know More</a>
                                        <a href="contact.php" class="service-action-btn contact-us">Contact Us</a>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="service-slide-card">
                                    <div class="service-slide-icon">
                                        <i class="fas fa-hand-holding-heart"></i>
                                    </div>
                                    <h3>Elderly Assistance</h3>
                                    <div class="service-hover-actions">
                                        <a href="services.php" class="service-action-btn know-more">Know More</a>
                                        <a href="contact.php" class="service-action-btn contact-us">Contact Us</a>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="service-slide-card">
                                    <div class="service-slide-icon">
                                        <i class="fas fa-walking"></i>
                                    </div>
                                    <h3>Physical Therapy</h3>
                                    <div class="service-hover-actions">
                                        <a href="services.php" class="service-action-btn know-more">Know More</a>
                                        <a href="contact.php" class="service-action-btn contact-us">Contact Us</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- Dynamic services from database -->
                <div class="services-slider">
                    <div class="swiper-container services-swiper">
                        <div class="swiper-wrapper">
                            <?php foreach ($servicesHandler->getAllActiveServices() as $service): ?>
                                <div class="swiper-slide">
                                    <div class="service-slide-card">
                                        <div class="service-slide-icon">
                                            <i class="fas <?php echo htmlspecialchars($service['icon']); ?>"></i>
                                        </div>
                                        <h3><?php echo htmlspecialchars($service['title']); ?></h3>
                                        <div class="service-hover-actions">
                                            <a href="services.php#service-<?php echo $service['id']; ?>" class="service-action-btn know-more">Know More</a>
                                            <a href="contact.php" class="service-action-btn contact-us">Contact Us</a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <div class="services-slider-navigation">
                        <div class="swiper-button-prev services-prev"></div>
                        <div class="swiper-button-next services-next"></div>
                    </div>

                    <!-- Pagination -->
                    <div class="swiper-pagination services-pagination"></div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Why Choose Us -->
<section class="why-choose-us">
    <div class="container">
        <div class="section-header" data-aos="fade-up">
            <h2>Why Choose Us?</h2>
            <p>Experience the difference with our specialized care approach</p>
        </div>
        <div class="features-grid">
            <div class="feature-card" data-aos="fade-up">
                <i class="fas fa-certificate"></i>
                <h3>Licensed Professionals</h3>
                <p>All our caregivers are certified and extensively trained.</p>
            </div>
            <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
                <i class="fas fa-clock"></i>
                <h3>24/7 Availability</h3>
                <p>Round-the-clock support whenever you need us.</p>
            </div>
            <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                <i class="fas fa-home"></i>
                <h3>Home Environment</h3>
                <p>Care provided in the comfort of your own home.</p>
            </div>
            <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
                <i class="fas fa-heart"></i>
                <h3>Personalized Care</h3>
                <p>Customized care plans tailored to your needs.</p>
            </div>
        </div>
    </div>
</section>

<!-- Stats Counter -->
<section class="stats-section">
    <div class="container">
        <div class="stats-grid">
            <div class="stat-card" data-aos="fade-up">
                <i class="fas fa-calendar-check"></i>
                <span class="stats-number" data-value="4">0</span>
                <p>Years of Experience</p>
            </div>
            <div class="stat-card" data-aos="fade-up" data-aos-delay="100">
                <i class="fas fa-users"></i>
                <span class="stats-number" data-value="5000">0</span>
                <p>Patients Served</p>
            </div>
            <div class="stat-card" data-aos="fade-up" data-aos-delay="200">
                <i class="fas fa-user-md"></i>
                <span class="stats-number" data-value="100">0</span>
                <p>Healthcare Professionals</p>
            </div>
        </div>    </div>
</section>

<!-- Our Partners Section -->
<section class="partners-section" data-aos="fade-up">
    <div class="container">
        <div class="section-header">
            <h2>Our Trusted Partners</h2>
            <p>Collaborating with leading healthcare organizations to provide you the best care</p>
        </div>
        
        <?php if (!empty($partners)): ?>
        <div class="partners-slider-container">
            <div class="partners-slider-track">
                <?php 
                // Duplicate partners for seamless loop
                $duplicatedPartners = array_merge($partners, $partners);
                foreach ($duplicatedPartners as $partner): 
                ?>                <div class="partner-logo" 
                     data-name="<?php echo htmlspecialchars($partner['name']); ?>"
                     <?php if ($partner['website_url']): ?>
                     onclick="window.open('<?php echo htmlspecialchars($partner['website_url']); ?>', '_blank')"
                     <?php endif; ?>>
                    <img src="<?php echo htmlspecialchars($partner['logo']); ?>" 
                         alt="<?php echo htmlspecialchars($partner['name']); ?>"
                         loading="lazy">
                </div>
                <?php endforeach; ?>
            </div>        </div>
        <?php else: ?>
        <div class="no-partners">
            <i class="fas fa-handshake"></i>
            <h3>Coming Soon</h3>
            <p>We're building partnerships with leading healthcare organizations.</p>
        </div>
        <?php endif; ?>
    </div>
</section>

<script>
// Adjust animation duration based on number of partners
document.addEventListener('DOMContentLoaded', function() {
    const track = document.querySelector('.partners-slider-track');
    const partnerCount = <?php echo count($partners); ?>;
    
    if (track && partnerCount > 0) {
        // Calculate animation duration: more partners = longer duration
        const baseDuration = 20; // Base duration in seconds
        const durationPerPartner = 2.5; // Additional seconds per partner
        const totalDuration = baseDuration + (partnerCount * durationPerPartner);
        
        track.style.animationDuration = totalDuration + 's';
    }
});
</script>

<?php
// Get testimonials data with fixed image paths
require_once 'includes/Testimonials.php';
$testimonialHandler = new Testimonials();
$testimonials = $testimonialHandler->getAllActiveTestimonialsForDisplay(); // Full content display
?>

<!-- Testimonials Section -->
<section class="testimonials">
    <div class="container">
        <div class="section-header">
            <h2>What Our Clients Say</h2>
            <p>Real experiences from families we've helped</p>
        </div>
        
        <div class="testimonials-slider">
            <!-- Swiper Container -->
            <div class="swiper-container testimonials-swiper">
                <div class="swiper-wrapper">
                    <?php if (empty($testimonials)): ?>
                        <!-- Fallback testimonials with full content -->
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <div class="testimonial-content">
                                    <p>"The care and attention provided by the doctors was exceptional. They made my recovery process so much more comfortable in my own home. The professional approach and genuine concern for my wellbeing really stood out."</p>
                                </div>
                                <div class="testimonial-author">
                                    <div class="testimonial-author-image no-image">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="author-info">
                                        <h4>Raj Thapa</h4>
                                        <p>Patient</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <div class="testimonial-content">
                                    <p>"Professional medical services at home made all the difference for our elderly father. The doctors were punctual, caring, and highly skilled. We couldn't have asked for better healthcare support."</p>
                                </div>
                                <div class="testimonial-author">
                                    <div class="testimonial-author-image no-image">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="author-info">
                                        <h4>Sita Sharma</h4>
                                        <p>Family Member</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <div class="testimonial-content">
                                    <p>"Excellent doorstep medical care with qualified doctors. The convenience and quality exceeded our expectations completely."</p>
                                </div>
                                <div class="testimonial-author">
                                    <div class="testimonial-author-image no-image">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="author-info">
                                        <h4>Ram Bahadur</h4>
                                        <p>Senior Citizen</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($testimonials as $testimonial): ?>
                            <div class="swiper-slide">
                                <div class="testimonial-card">
                                    <div class="testimonial-content">
                                        <p>"<?php echo htmlspecialchars($testimonial['display_content']); ?>"</p>
                                    </div>
                                    <div class="testimonial-author">
                                        <!-- Enhanced image path handling for webserver compatibility -->
                                        <?php if (!empty($testimonial['photo_path'])): ?>
                                            <div class="testimonial-author-image" style="background-image: url('<?php echo htmlspecialchars($testimonial['photo_path']); ?>'); background-size: cover; background-position: center;"></div>
                                        <?php else: ?>
                                            <div class="testimonial-author-image no-image">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div class="author-info">
                                            <h4><?php echo htmlspecialchars($testimonial['name']); ?></h4>
                                            <p><?php echo htmlspecialchars($testimonial['position']); ?></p>
                                            <?php if ($testimonial['rating'] > 0): ?>
                                            <div class="testimonial-rating">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star<?php echo $i <= $testimonial['rating'] ? '' : '-o'; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <!-- Add Pagination -->
                <div class="swiper-pagination"></div>
            </div>
            
            <!-- Add Navigation -->
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>
    </div>
</section>


<!-- Contact CTA -->
<section class="contact-cta">
    <div class="container">
        <div class="cta-content" data-aos="fade-up">
            <h2>Ready to Get Started?</h2>
            <p>Contact us today to schedule a free consultation and learn more about our services.</p>
            <div class="cta-buttons">
                <a href="contact.php" class="btn btn-primary">Contact Us</a>
                <a href="booking.php" class="btn btn-outline" style="color:white;">Book Consultation</a>
            </div>
        </div>
    </div>
</section>
<!-- Add this right before the closing body tag in index.php -->
<script>
// Testimonials and Services Slider Initialization
document.addEventListener('DOMContentLoaded', function() {
    // Initialize sliders
    if (typeof Swiper !== 'undefined') {
        // Initialize testimonials slider
        if (document.querySelector('.testimonials-swiper')) {
            initTestimonialsSlider();
        }

        // Initialize services slider
        if (document.querySelector('.services-swiper')) {
            initServicesSlider();
        }
    } else {
        // If Swiper isn't loaded yet, wait for a moment and try again
        setTimeout(function() {
            if (typeof Swiper !== 'undefined') {
                if (document.querySelector('.testimonials-swiper')) {
                    initTestimonialsSlider();
                }
                if (document.querySelector('.services-swiper')) {
                    initServicesSlider();
                }
            } else {
                console.error('Swiper library not loaded properly');
            }
        }, 1000);
    }
});

function initTestimonialsSlider() {
    // Simple configuration without complex animations
    var testimonialSwiper = new Swiper('.testimonials-swiper', {
        // Optional parameters
        slidesPerView: 1,
        spaceBetween: 30,
        loop: true,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
        // Responsive breakpoints
        breakpoints: {
            // when window width is >= 768px
            768: {
                slidesPerView: 2,
                spaceBetween: 20
            },
            // when window width is >= 1024px
            1024: {
                slidesPerView: 2,
                spaceBetween: 30
            }
        },
        // Navigation arrows
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        // Pagination
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        }
    });
}

// Services Slider Initialization
function initServicesSlider() {
    if (document.querySelector('.services-swiper')) {
        var servicesSwiper = new Swiper('.services-swiper', {
            // Basic settings
            slidesPerView: 2,
            spaceBetween: 20,
            loop: true,
            centeredSlides: false,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
            },

            // Responsive breakpoints
            breakpoints: {
                // when window width is >= 480px
                480: {
                    slidesPerView: 3,
                    spaceBetween: 15,
                    centeredSlides: false,
                },
                // when window width is >= 576px
                576: {
                    slidesPerView: 4,
                    spaceBetween: 20,
                    centeredSlides: false,
                },
                // when window width is >= 768px
                768: {
                    slidesPerView: 5,
                    spaceBetween: 25,
                    centeredSlides: false,
                },
                // when window width is >= 1024px
                1024: {
                    slidesPerView: 6,
                    spaceBetween: 30,
                    centeredSlides: false,
                },
                // when window width is >= 1200px
                1200: {
                    slidesPerView: 7,
                    spaceBetween: 30,
                    centeredSlides: false,
                }
            },

            // Navigation arrows
            navigation: {
                nextEl: '.services-next',
                prevEl: '.services-prev',
            },

            // Pagination
            pagination: {
                el: '.services-pagination',
                clickable: true,
                dynamicBullets: true,
            },

            // Effects
            effect: 'slide',
            speed: 800,

            // Events
            on: {
                init: function() {
                    console.log('Services slider initialized');
                },
                slideChange: function() {
                    // Optional: Add any slide change effects here
                }
            }
        });
    }
}
</script>
<?php include 'includes/footer.php'; ?>
